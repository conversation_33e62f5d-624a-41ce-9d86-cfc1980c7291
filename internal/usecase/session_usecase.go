package usecase

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/repository"
	"github.com/smooth-inc/backend/pkg/errors"
)

type sessionUsecase struct {
	sessionRepo         repository.SessionRepository
	userRepo            repository.UserRepository
	plateRepo           repository.PlateRepository
	parkingLotRepo      repository.ParkingLotRepository
	autoPaymentProcessor AutoPaymentProcessor
	notificationRepo    repository.NotificationRepository
}

func NewSessionUsecase(
	sessionRepo repository.SessionRepository,
	userRepo repository.UserRepository,
	plateRepo repository.PlateRepository,
	parkingLotRepo repository.ParkingLotRepository,
	autoPaymentProcessor AutoPaymentProcessor,
	notificationRepo repository.NotificationRepository,
) SessionUsecase {
	return &sessionUsecase{
		sessionRepo:         sessionRepo,
		userRepo:            userRepo,
		plateRepo:           plateRepo,
		parkingLotRepo:      parkingLotRepo,
		autoPaymentProcessor: autoPaymentProcessor,
		notificationRepo:    notificationRepo,
	}
}

func (uc *sessionUsecase) Start(ctx context.Context, parkingLotID uuid.UUID, plateID *uuid.UUID, userID *uuid.UUID) (*domain.Session, error) {
	_, err := uc.parkingLotRepo.GetByID(ctx, parkingLotID)
	if err != nil {
		return nil, errors.NewNotFoundError("parking lot not found")
	}

	if plateID != nil {
		activeSession, err := uc.sessionRepo.GetActiveByPlateID(ctx, *plateID)
		if err == nil && activeSession != nil {
			return nil, errors.NewConflictError("plate already has an active session")
		}
	}

	session, err := domain.NewSession(parkingLotID, plateID, userID)
	if err != nil {
		return nil, errors.NewValidationError(err.Error())
	}

	if err := uc.sessionRepo.Create(ctx, session); err != nil {
		return nil, errors.NewDatabaseError("failed to create session", err)
	}

	if userID != nil {
		uc.createParkingStartedNotification(ctx, *userID, session)
	}

	return session, nil
}

func (uc *sessionUsecase) Complete(ctx context.Context, sessionID uuid.UUID, exitTime string) error {
	session, err := uc.sessionRepo.GetByID(ctx, sessionID)
	if err != nil {
		return errors.NewNotFoundError("session not found")
	}

	if !session.CanBeCompleted() {
		return errors.NewBadRequestError("session cannot be completed")
	}

	exitTimeParsed, err := time.Parse(time.RFC3339, exitTime)
	if err != nil {
		return errors.NewValidationError("invalid exit time format")
	}

	finalAmount, err := uc.calculateParkingFee(ctx, session, exitTimeParsed)
	if err != nil {
		return errors.NewInternalError("failed to calculate parking fee")
	}

	if err := session.CompleteSession(exitTimeParsed, finalAmount); err != nil {
		return errors.NewValidationError(err.Error())
	}

	if err := uc.sessionRepo.Update(ctx, session); err != nil {
		return errors.NewDatabaseError("failed to update session", err)
	}

	if session.UserID != nil {
		uc.createParkingCompletedNotification(ctx, *session.UserID, session)
		
		if session.CanProcessAutoPayment() {
			go uc.processAutoPaymentAsync(ctx, session)
		}
	}

	return nil
}

func (uc *sessionUsecase) GetByID(ctx context.Context, id uuid.UUID) (*domain.Session, error) {
	session, err := uc.sessionRepo.GetByID(ctx, id)
	if err != nil {
		return nil, errors.NewNotFoundError("session not found")
	}
	return session, nil
}

func (uc *sessionUsecase) GetActiveByPlateID(ctx context.Context, plateID uuid.UUID) (*domain.Session, error) {
	session, err := uc.sessionRepo.GetActiveByPlateID(ctx, plateID)
	if err != nil {
		return nil, errors.NewNotFoundError("active session not found")
	}
	return session, nil
}

func (uc *sessionUsecase) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Session, error) {
	sessions, err := uc.sessionRepo.GetByUserID(ctx, userID, limit, offset)
	if err != nil {
		return nil, errors.NewDatabaseError("failed to get sessions", err)
	}
	return sessions, nil
}

func (uc *sessionUsecase) calculateParkingFee(ctx context.Context, session *domain.Session, exitTime time.Time) (int, error) {
	duration := exitTime.Sub(session.EntryTime)
	hours := int(duration.Hours())
	if duration.Minutes() > float64(hours*60) {
		hours++
	}

	baseRate := 200
	finalAmount := hours * baseRate

	if finalAmount > 2000 {
		finalAmount = 2000
	}

	return finalAmount, nil
}

func (uc *sessionUsecase) processAutoPaymentAsync(ctx context.Context, session *domain.Session) {
	user, err := uc.userRepo.GetByID(ctx, *session.UserID)
	if err != nil {
		return
	}

	if !user.CanProcessAutoPayment() {
		return
	}

	_, err = uc.autoPaymentProcessor.ProcessAutoPayment(ctx, session.ID)
	if err != nil {
		uc.createPaymentFailedNotification(ctx, *session.UserID, session, err.Error())
	}
}

func (uc *sessionUsecase) createParkingStartedNotification(ctx context.Context, userID uuid.UUID, session *domain.Session) {
	title := session.GetNotificationTitle()
	message := session.GetNotificationMessage()
	
	notification, err := domain.NewNotification(userID, domain.NotificationTypeParked, title, message)
	if err != nil {
		return
	}
	
	notification.SetSessionID(session.ID)
	uc.notificationRepo.Create(ctx, notification)
}

func (uc *sessionUsecase) createParkingCompletedNotification(ctx context.Context, userID uuid.UUID, session *domain.Session) {
	title := session.GetNotificationTitle()
	message := session.GetNotificationMessage()
	
	notification, err := domain.NewNotification(userID, domain.NotificationTypeParked, title, message)
	if err != nil {
		return
	}
	
	notification.SetSessionID(session.ID)
	uc.notificationRepo.Create(ctx, notification)
}

func (uc *sessionUsecase) createPaymentFailedNotification(ctx context.Context, userID uuid.UUID, session *domain.Session, errorMessage string) {
	title := "Auto Payment Failed"
	message := fmt.Sprintf("Automatic payment for your parking session failed: %s. Please pay manually.", errorMessage)
	
	notification, err := domain.NewNotification(userID, domain.NotificationTypePaymentCompleted, title, message)
	if err != nil {
		return
	}
	
	notification.SetSessionID(session.ID)
	uc.notificationRepo.Create(ctx, notification)
}
